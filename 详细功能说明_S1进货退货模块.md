# S1【进货/退货】模块详细功能说明

## 当前分析模块：S1【进货/退货】
**状态：✅ 已完成分析**

---

## 功能概述
S1【进货/退货】模块是ZGZN POS系统的库存管理核心模块，提供完整的商品进货、退货、批次批号管理、批量导入以及供应商管理功能。该模块支持多种进货方式，并具备完善的批次追溯和保质期管理能力。

---

## 详细功能列表

### 1. 批次批号融合功能

#### 1.1 进货基础功能
**代码位置：**
- `src/page/pc/stock.vue` (4036-4083行) - 进货提交逻辑
- `src/common/service/purchaseService.js` (14-61行) - 进货服务接口

##### 1.1.1 进货（空状态）
**代码位置：** `src/page/pc/stock.vue` (398-416行)
**功能特性：**
- 空状态展示界面设计
- 进货引导提示
- 新手操作引导

##### 1.1.2 进货（选择商品）
**代码位置：** `src/page/pc/stock.vue` (270-400行)
**功能特性：**
- 商品搜索和选择界面
- 分类筛选功能
- 扫码快速添加商品
- 商品信息实时显示

##### 1.1.3 进货（已选商品）
**代码位置：** `src/page/pc/stock.vue` (4036-4066行)
**功能特性：**
- 已选商品列表管理
- 数量调整功能
- 价格修改功能
- 折扣率设置
- 明细信息编辑

##### 1.1.4 进货（已选-切换编码）⭐
**功能特性：**
- 支持条码切换功能
- 一品多码商品管理
- 编码规则切换

##### 1.1.5 进货（改价、折扣率、明细）
**代码位置：** `src/page/pc/stock.vue` (4037-4045行)
**功能特性：**
- **进货价格调整** (4037-4045行)：支持单个商品改价
- **折扣率设置** (4039行)：整单折扣功能
- **进货备注** (4040行)：进货单备注信息
- **最终金额** (4042行)：折后金额计算

##### 1.1.6 进货（提交进货单）
**代码位置：** 
- `src/page/pc/stock.vue` (4048-4066行) - 进货提交
- `src/common/service/purchaseService.js` (14-110行) - 进货处理逻辑

**功能特性：**
- **供应商关联** (4049行)：关联供应商信息
- **库存更新** (4050-4066行)：自动更新商品库存
- **加权平均** (4044行)：支持加权平均计价
- **交接班记录** (4061-4065行)：关联交接班管理

#### 1.2 退货功能
**代码位置：**
- `src/page/pc/stock.vue` (4070-4082行) - 退货逻辑
- `src/common/service/purchaseService.js` (38-61行) - 退货服务

##### 1.2.1 退货（已选商品）
**功能特性：**
- 退货商品选择
- 退货数量确认
- 退货原因填写

##### 1.2.2 退货（已选-切换编码）⭐
**功能特性：**
- 退货商品编码切换
- 批次信息验证

##### 1.2.3 退货（选择批号）
**代码位置：** `src/common/service/purchaseService.js` (49-60行)
**功能特性：**
- **批次分组** (49行)：按供应商指纹分组退货
- **批号选择** (50-58行)：支持多批次退货
- **库存扣减** (78行)：负数处理退货库存

### 2. 进货批量导入功能

#### 2.1 批量导入主流程
**代码位置：**
- `src/common/service/purchaseService.js` (354-393行) - 批量导入主逻辑
- `src/page/pc/goods.vue` (1925-1950行) - 导入界面配置

##### 2.1.1 进货（主弹窗）
**功能特性：**
- 批量导入入口界面
- 文件选择功能
- 导入模板下载

##### 2.1.2 进货（上传文件导入中）
**代码位置：** `src/common/service/purchaseService.js` (354-363行)
**功能特性：**
- 文件上传进度显示
- 导入状态反馈
- 错误信息提示

##### 2.1.3 进货（Excel 导入模板）
**代码位置：** `src/page/pc/goods.vue` (1928-1947行)
**功能特性：**
- **字段映射** (1928-1947行)：标准化字段映射
  - 货号（必填）、商品名称（必填）
  - 分类、单位、品牌信息
  - 价格信息（售价、会员价、进货价）
  - 库存和规格信息

##### 2.1.4 进货（导入中发现新商品）
**代码位置：** `src/common/service/purchaseService.js` (364-393行)
**功能特性：**
- **数据验证** (364-392行)：多层次数据检查
- **商品存在性检查** (381-389行)：验证商品是否存在
- **称重商品验证** (373行)：特殊商品类型检查

##### 2.1.5 进货（新增商品）
**功能特性：**
- 导入过程中自动创建新商品
- 商品信息补全
- 分类和单位自动匹配

##### 2.1.6 进货（导入成功）
**代码位置：** `src/common/service/purchaseService.js` (331行)
**功能特性：**
- 导入结果统计
- 成功商品列表
- 库存更新确认

##### 2.1.7 进货（批量失败）
**代码位置：** `src/common/service/purchaseService.js` (376-380行)
**功能特性：**
- **错误信息收集** (376行)：详细错误信息
- **失败原因分析** (377-379行)：检查失败和商品不存在
- 失败数据导出功能

### 3. 供应商管理

#### 3.1 供应商管理主界面
**代码位置：**
- `src/components/pc_supplier_manage.vue` (213-275行) - 供应商管理界面
- `src/components/Header.vue` (1706-1710行) - 供应商管理入口

**功能特性：**
- **搜索功能** (230-241行)：支持供应商名称、联系人、电话搜索
- **状态筛选** (252-264行)：启用/禁用状态筛选
- **新增入口** (268-271行)：新增供应商按钮

#### 3.2 供应商管理（新增/编辑）
**代码位置：**
- `src/components/pc_add_edit_supplier.vue` (169-472行) - 供应商新增编辑组件

##### 3.2.1 新增供应商
**代码位置：** `src/components/pc_add_edit_supplier.vue` (398-414行)
**功能特性：**
- **基本信息** (328-337行)：供应商名称、联系人、手机号、地址、备注
- **唯一性验证** (402行)：生成唯一标识fingerprint
- **重复检查** (409-411行)：供应商名称重复检查
- **创建记录** (401-408行)：创建者和时间记录

##### 3.2.2 编辑供应商
**代码位置：** `src/components/pc_add_edit_supplier.vue` (415-435行)
**功能特性：**
- **信息修改** (415-434行)：供应商信息更新
- **修改记录** (420行)：修改者记录
- **名称验证** (429-431行)：修改时的名称重复检查

##### 3.2.3 供应商状态管理
**代码位置：** `src/components/pc_add_edit_supplier.vue` (348-383行)
**功能特性：**
- **禁用供应商** (348-354行,370-383行)：支持供应商禁用
- **关联商品检查** (349-351行)：检查供应商下是否有关联商品
- **启用供应商** (355-368行)：禁用后可重新启用
- **风险提示** (350行)：禁用时的商品关联风险提醒

### 4. 批次批号管理功能

#### 4.1 生产日期和保质期管理
**代码位置：**
- `src/components/pc_add_goods.vue` (1427-1465行) - 保质期设置
- `src/components/pc_stock_overdue_warning_detail.vue` (448-494行) - 过期预警

##### 4.1.1 保质期计算功能
**代码位置：** `src/components/pc_add_goods.vue` (1810-1828行)
**功能特性：**
- **生产日期选择** (1431-1442行)：日期选择器组件
- **保质期天数** (1450-1461行)：支持手动输入和计算
- **有效期计算** (1810-1818行)：自动计算到期日期
- **计算器功能** (1459行)：保质期天数计算弹窗

##### 4.1.2 批次信息管理
**代码位置：** `src/components/pc_add_goods.vue` (1748-1749行)
**功能特性：**
- **生产日期记录** (1749行)：商品生产日期管理
- **保质期天数** (1748行)：保质期天数设置
- **批次追溯** ：支持按批次追溯商品信息

#### 4.2 过期预警管理
**代码位置：** `src/components/pc_stock_overdue_warning_detail.vue` (452-489行)
**功能特性：**
- **单个修改** (452-458行)：单个商品批次信息修改
- **批量编辑** (459-472行)：批量修改生产日期和保质期
- **批次验证** (473-489行)：修改时的数据验证

### 5. 高级功能

#### 5.1 加权平均计价
**代码位置：** `src/page/pc/stock.vue` (4044行)
**功能特性：**
- 支持加权平均成本计算
- 库存成本动态调整
- 成本核算准确性保障

#### 5.2 供应商关联管理
**代码位置：**
- `src/page/pc/stock.vue` (4049行,4051-4055行)
- `src/page/pc/goods.vue` (2356-2368行)

**功能特性：**
- **进货关联** (4049行)：进货时关联供应商
- **批量设置** (2356-2368行)：批量设置商品供应商
- **供应商更新** (4051-4055行)：进货时更新供应商信息

#### 5.3 权限控制
**代码位置：** `src/components/Header.vue` (1706-1714行)
**功能特性：**
- **供应商管理权限** (1706行)：supplier_management权限
- **进货权限** (1711行)：purchase权限控制
- **退货权限** (1713行)：return_purchase权限控制

---

## 技术实现特点

### 1. 数据完整性保障
- 多层次数据验证机制
- 业务逻辑完整性检查
- 事务处理保证数据一致性

### 2. 批次追溯能力
- 完整的批次批号管理
- 生产日期和保质期跟踪
- 过期预警和提醒机制

### 3. 灵活的导入方式
- Excel批量导入支持
- 字段映射和数据验证
- 错误处理和重试机制

### 4. 供应商集成管理
- 供应商信息完整管理
- 商品与供应商关联
- 采购历史跟踪

---

## 功能代码引用位置总结

| 功能模块 | 主要代码位置 | 核心文件 |
|---------|-------------|----------|
| 进货基础功能 | stock.vue:4036-4083 | src/page/pc/stock.vue |
| 退货功能 | purchaseService.js:38-61 | src/common/service/purchaseService.js |
| 批量导入 | purchaseService.js:354-393 | src/common/service/purchaseService.js |
| 供应商管理 | pc_supplier_manage.vue:213-275 | src/components/pc_supplier_manage.vue |
| 供应商编辑 | pc_add_edit_supplier.vue:169-472 | src/components/pc_add_edit_supplier.vue |
| 批次管理 | pc_add_goods.vue:1427-1465 | src/components/pc_add_goods.vue |
| 过期预警 | pc_stock_overdue_warning_detail.vue:448-494 | src/components/pc_stock_overdue_warning_detail.vue |

---

**分析完成时间：** 当前
**下一步：** 继续分析S1【商品】模块
