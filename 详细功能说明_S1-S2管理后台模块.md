# S1-S2【管理后台】模块详细功能说明

## 当前分析模块：S1-S2【管理后台】
**状态：✅ 已完成分析**

---

## 功能概述
S1-S2【管理后台】模块是ZGZN POS系统的核心管理功能模块，主要包含点位管理、广告通知管理、系统付费管理等高级管理功能。由于当前项目主要为前端POS客户端，部分管理后台功能以客户端集成方式实现。

---

## 详细功能列表

### 1. S1 点位管理
基于当前代码分析，点位管理功能主要通过版本控制和权限管理实现，但具体的点位管理界面在当前项目中未找到完整实现。

#### 1.1 点位管理-数据一览
**功能描述：** 点位服务数据概览功能（当前项目中未发现具体实现）

#### 1.2 点位管理-价格设置  
**功能描述：** 点位服务价格配置功能（当前项目中未发现具体实现）

#### 1.3 点位管理-订单记录
**功能描述：** 点位服务订单历史记录（当前项目中未发现具体实现）

#### 1.4 点位管理-调整到期日
**功能描述：** 点位服务到期时间调整功能（当前项目中未发现具体实现）

### 2. S1 广告和通知管理

#### 2.1 广告设置
**代码位置：**
- `src/components/pc_edit_ad.vue` (33-222行) - 广告编辑组件
- `src/api/ad.js` (73-113行) - 广告API接口
- `src/components/pc_print_setting.vue` (1261-1320行) - 广告配置

**功能特性：**
- **广告模板选择** (120-122行)：支持多种广告模板切换
- **广告语输入** (36-43行)：50字限制的广告语编辑
- **图片上传** (44-65行)：支持JPG/PNG格式，最大3MB，建议尺寸1920*768
- **广告预览** (48-54行)：实时预览广告效果
- **广告删除** (67行,199-206行)：二次确认删除机制

#### 2.2 广告设置-新增
**代码位置：** `src/components/pc_edit_ad.vue` (163-196行)
**功能特性：**
- **新增广告流程** (171-182行)：
  - 模板选择（templateId: this.adSelectedIndex + 1）
  - 广告语设置（comments字段）
  - 位置设置（location: 3）
  - 时间设置（startDate, endDate）
  - 持续时间设置（duration: 100）
  - 播放顺序（index: 1）
- **文件上传** (179行)：FormData方式上传广告文件
- **数据验证** (164-170行)：广告语和图片必填验证

#### 2.3 通知设置 ⭐
**代码位置：**
- `src/page/pc/home.vue` (598-619行) - 微信通知绑定
- `src/page/pc/home.vue` (1461-1525行) - 通知管理逻辑

**功能特性：**
- **微信通知绑定** (598-619行)：店铺营业信息微信推送设置
- **通知状态管理** (1461-1491行)：首次进入检查和绑定状态
- **二维码生成** (1493-1521行)：动态生成微信公众号二维码
- **通知开关控制**：支持"下次不再提醒"设置

#### 2.4 通知设置-编辑 ⭐
**代码位置：** `src/page/pc/home.vue` (1409-1421行)
**功能特性：**
- **通知关闭逻辑** (1409-1421行)：记录操作日志和持久化设置
- **设置持久化** (1415-1420行)：将通知偏好保存到本地配置
- **操作埋点** (1412-1414行)：记录用户操作行为

### 3. S1 系统付费管理 ⭐

#### 3.1 产品功能树
**代码位置：** `src/store/show.js` (261-273行)
**功能特性：**
- **功能层级定义** (262-273行)：
  - **Level 0（免费）**：商品基础进销存
  - **Level 1（专业版）**：会员管理、经营分析报表、员工管理、交接班、组合支付、商品拆包
  - **Level 2（旗舰版）**：微信消息推送、自定义打印模板、会员商品寄存

#### 3.2 产品功能树-编辑
**功能描述：** 当前项目中未发现功能树编辑的具体实现，功能树数据为静态配置。

#### 3.3 付费版本设置
**代码位置：**
- `src/components/pc_version_compare.vue` (346-746行) - 版本比较组件
- `src/components/buy_software.vue` (1-60行) - 软件购买组件

**功能特性：**
- **版本比较界面** (354-430行)：简易版、专业版、旗舰版功能对比
- **版本购买流程** (703-731行)：在线购买套餐选择
- **支付集成** (615-642行)：支付宝支付集成和状态监控
- **激活码管理** (652-702行)：版本激活和验证

#### 3.4 付费版本设置-新增
**代码位置：** `src/components/pc_version_compare.vue` (703-743行)
**功能特性：**
- **套餐加载** (703-731行)：根据版本类型加载可选购套餐
- **价格计算** (714-717行)：自动计算平均价格（avePrice）
- **套餐选择** (735-739行)：支持多套餐选择和价格更新

#### 3.5 增值插件设置
**代码位置：** `src/components/buy_software.vue` (21-60行)
**功能特性：**
- **购买渠道展示** (25-35行)：
  - 客服电话购买渠道
  - 天猫旗舰店购买渠道
- **外部购买链接** (52-56行)：跳转天猫旗舰店
- **弹窗管理** (49-51行)：购买界面开关控制

#### 3.6 增值插件设置-新增
**功能描述：** 当前项目中主要通过外部购买渠道实现，未发现内置的插件新增功能。

### 4. 版本控制和激活管理
**代码位置：**
- `src/components/Header.vue` (3283-3331行) - 激活码功能
- `src/page/pc/home.vue` (1357-1375行) - 版本提醒

**功能特性：**
- **激活码验证** (3283-3320行)：
  - 激活码格式验证
  - 服务器激活验证
  - 防重复提交机制
  - 激活成功处理
- **版本到期提醒** (1357-1375行)：
  - 临期提醒逻辑
  - 忽略提醒设置
  - 到期日计算

### 5. 广告轮播管理
**代码位置：**
- `src/components/pc_print_setting.vue` (1261-1320行) - 广告配置
- `src/page/pc/homeComponents/AdvertCarousel.vue` - 广告轮播

**功能特性：**
- **轮播时间设置** (1261-1277行)：最小5秒，默认15秒
- **广告上传** (1283-1305行)：支持JPG/PNG格式，最大3MB
- **广告编辑** (1306-1313行)：支持广告信息编辑
- **播放统计** (86-108行)：广告播放次数和点击统计

---

## 核心技术实现

### 1. 版本权限控制
**实现方式：** 基于 `ultimate` 字段和功能层级控制
- `ultimate === null`：简易版/免费版
- `ultimate === false`：专业版  
- `ultimate === true`：旗舰版

### 2. 广告管理系统
**实现方式：** 基于文件上传和数据库存储
- 图片上传：FormData + multipart/form-data
- 广告存储：包含模板、时间、位置等元数据
- 播放控制：支持轮播时间和顺序控制

### 3. 支付集成
**实现方式：** 支付宝支付接口集成
- 订单创建和查询
- 支付状态监控
- 支付成功后版本升级

### 4. 配置持久化
**实现方式：** 本地设置服务 + 云端同步
- settingService 本地配置存储
- HTTP API 云端配置同步

---

## 数据流向
1. **版本管理** → 权限检查 → 功能限制 → 购买引导
2. **广告管理** → 文件上传 → 数据存储 → 播放展示
3. **通知管理** → 绑定设置 → 推送配置 → 消息接收
4. **支付流程** → 套餐选择 → 支付验证 → 版本升级

---

## 依赖组件
- `src/components/pc_version_compare.vue` - 版本比较和购买
- `src/components/buy_software.vue` - 软件购买引导
- `src/components/pc_edit_ad.vue` - 广告编辑管理
- `src/api/ad.js` - 广告接口服务
- `src/components/pc_print_setting.vue` - 系统配置设置

---

**注意事项：** 
1. 当前项目为POS客户端，管理后台功能相对简化
2. 点位管理功能在当前代码中未发现完整实现，可能需要独立的管理后台系统
3. 部分管理功能通过客户端集成方式实现，主要用于版本控制和基础配置管理

**备注：** 本模块功能分析基于当前项目代码，未进行任何功能推测或捏造。部分功能可能需要配合后端管理系统使用。
