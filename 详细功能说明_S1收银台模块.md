# S1【收银台】模块详细功能说明

## 当前分析模块：S1【收银台】
**状态：✅ 已完成分析**

---

## 功能概述
S1【收银台】模块是ZGZN POS系统的核心业务处理模块，提供完整的收银、支付、退货、优惠、会员管理等功能。该模块支持多种支付方式、批次批号管理、一品多码、优惠折扣等高级功能，是整个POS系统的业务中心。

---

## 详细功能列表

### 1. 批次批号融合功能

#### 1.1 收银台-批次商品
**功能特性：**
- 批次商品识别和选择
- 批次库存显示
- 保质期提醒

#### 1.2 收银台-切换批次
**功能特性：**
- 批次切换界面
- 批次信息对比
- 批次优先级选择

### 2. 收银台基础功能

#### 2.1 收银台（空状态）
**代码位置：** `src/page/pc/pay.vue` (107-300行)
**功能特性：**
- 空状态界面展示
- 操作引导信息
- 快捷操作提示

#### 2.2 收银台（有商品）
**代码位置：** `src/page/pc/pay.vue` (4530-4575行) - 扫码添加商品
**功能特性：**
- **商品列表展示** ：已添加商品的详细信息显示
- **扫码添加商品** (4530-4575行)：支持条码扫描快速添加
- **传秤商品处理** (4532-4575行)：20开头条码的传秤商品特殊处理
- **数量和价格显示** ：实时计算商品小计和总计

#### 2.3 收银台（手选商品）
**功能特性：**
- 商品手动选择界面
- 商品搜索功能
- 分类浏览选择

#### 2.4 收银台（优惠明细）
**功能特性：**
- 优惠明细展示
- 优惠计算过程
- 优惠规则说明

#### 2.5 收银台（编辑商品-普通）
**功能特性：**
- 商品信息编辑
- 价格和数量调整
- 商品删除功能

#### 2.6 收银台（编辑商品-一品多码）
**功能特性：**
- 一品多码商品编辑
- 条码切换功能

#### 2.7 收银台（一品多码-切换码）⭐
**功能特性：**
- 多条码切换界面
- 条码验证和确认
- 库存状态同步

#### 2.8 收银台（直接收款）
**功能特性：**
- 快速收款模式
- 无商品收款
- 金额直接输入

### 3. 优惠折扣功能
**代码位置：** `src/page/pc/pay.vue` (1158-1217行)

#### 3.1 收银台（整单折扣）
**代码位置：** `src/page/pc/pay.vue` (1162-1176行)
**功能特性：**
- **预设折扣** (1170-1172行)：多种预设折扣选项（如9.5折、9折等）
- **自定义折扣** (1173-1175行)：支持自定义折扣比例
- **折扣编辑** (1164-1167行)：支持折扣选项的编辑和修改

#### 3.2 收银台（编辑整单折扣）
**代码位置：** `src/page/pc/pay.vue` (3090-3134行)
**功能特性：**
- **折扣设置保存** (3127-3134行)：折扣设置的保存和更新
- **折扣验证** (3098-3105行)：折扣值验证，不允许设置为0
- **折扣应用** (3135-3145行)：将折扣应用到当前订单

#### 3.3 收银台（整单减价）
**代码位置：** `src/page/pc/pay.vue` (1177-1192行)
**功能特性：**
- **预设减价** (1186-1188行)：多种预设减价金额
- **自定义减价** (1189-1191行)：支持自定义减价金额
- **减价编辑** (1180-1183行)：支持减价选项的编辑

#### 3.4 收银台（抹零方式）
**代码位置：** `src/page/pc/pay.vue` (1193-1217行)
**功能特性：**
- **不抹零** (1196-1199行)：保持原价格
- **抹分** (1200-1203行)：去掉分位
- **抹角** (1204-1207行)：去掉角位
- **四舍五入到角** (1208-1211行)：按四舍五入规则处理
- **逢分进角** (1212-1215行)：有分就进到下一角

### 4. 订单管理功能

#### 4.1 收银台（取单）
**代码位置：** `src/page/pc/pay.vue` (128-257行)
**功能特性：**
- **挂单列表** (141-251行)：显示所有挂起的订单
- **订单搜索** (132-139行)：支持备注或会员手机号搜索
- **订单详情** (146-248行)：显示订单商品明细和金额信息
- **取单操作** (247行)：恢复挂起的订单到当前收银界面
- **删除订单** (245行)：删除不需要的挂单
- **清空挂单** (252-255行)：批量清空所有挂单

#### 4.2 收银台（备注）
**代码位置：** `src/page/pc/pay.vue` (4166-4173行)
**功能特性：**
- **备注输入** (4168-4172行)：支持为订单添加备注信息
- **快捷键支持** (4166行)：Ctrl+B快速添加备注
- **备注显示** ：订单和小票中显示备注信息

### 5. 会员功能

#### 5.1 收银台（选择会员初始）
**功能特性：**
- 会员搜索界面
- 会员列表展示
- 新增会员入口

#### 5.2 收银台（选择会员结果页）
**代码位置：** `src/page/pc/pay.vue` (1138-1157行)
**功能特性：**
- **会员信息显示** (1142行)：显示找到的会员数量
- **会员分页** (1143-1153行)：支持会员列表分页浏览
- **会员选择** ：选择特定会员应用到当前订单

### 6. 支付结算功能
**代码位置：** `src/components/pc_final_pay.vue` (3454-5513行)

#### 6.1 收银台（扫码支付-未开通）
**功能特性：**
- 扫码支付开通引导
- 功能介绍和优势说明
- 开通流程指导

#### 6.2 收银台（扫码支付-已开通）
**代码位置：** `src/components/pc_final_pay.vue` (3488-3490行)
**功能特性：**
- **扫码识别** (3488-3490行)：自动识别微信或支付宝付款码
- **扫码支付处理** ：调用相应支付接口完成支付
- **支付状态监控** ：实时监控支付状态

#### 6.3 收银台（现金支付）
**代码位置：** `src/page/pc/pay.vue` (4133-4136行)
**功能特性：**
- **现金快捷支付** (4133-4136行)：Alt+X快捷键现金支付
- **找零计算** (4475-4477行)：自动计算找零金额
- **找零提醒** (4475-4477行)：找零超过200元的安全提示

#### 6.4 收银台（线下支付）
**代码位置：** `src/page/pc/pay.vue` (4137-4148行)
**功能特性：**
- **微信线下支付** (4137-4140行)：Alt+W快捷键微信支付
- **支付宝线下支付** (4141-4144行)：Alt+Z快捷键支付宝支付
- **POS支付** (4145-4148行)：Alt+P快捷键POS机支付

#### 6.5 收银台（储值支付）
**代码位置：** `src/components/pc_final_pay.vue` (5202-5234行)
**功能特性：**
- **会员余额验证** (5218-5223行)：检查会员余额是否足够
- **支付密码验证** (5212-5217行)：验证会员支付密码
- **余额扣减** (5227-5234行)：从会员账户扣减相应金额

#### 6.6 收银台（组合支付）-初版
**代码位置：** `src/components/pc_final_pay.vue` (1614-1640行,5468-5513行)

##### 6.6.1 支付方式选择
**代码位置：** `src/components/pc_final_pay.vue` (2501-2537行)
**功能特性：**
- **支付方式配置** (2501-2537行)：现金、POS、微信、支付宝、会员卡等多种支付方式
- **支付方式切换** (1622-1639行)：整单支付和组合支付模式切换
- **快捷键支持** ：各支付方式对应的快捷键操作

##### 6.6.2 组合支付逻辑
**代码位置：** `src/components/pc_final_pay.vue` (5418-5475行)
**功能特性：**
- **金额验证** (5419-5441行)：验证各支付方式金额的合理性
- **组合支付处理** (5468-5475行)：处理多种支付方式的组合
- **会员支付集成** (5442-5467行)：会员支付在组合支付中的特殊处理

#### 6.7 收银台-支付成功
**代码位置：** `src/components/pc_final_pay.vue` (4925-4948行)
**功能特性：**
- **支付完成提示** (4925-4945行)：收银完成或退货完成的成功提示
- **钱箱控制** (4947行)：支付成功后自动打开钱箱
- **客显控制** (4940行)：更新客显屏幕显示
- **小票打印** ：自动触发小票打印

### 7. 快捷键功能
**代码位置：** `src/page/pc/pay.vue` (4126-4175行)

#### 7.1 支付快捷键
**功能特性：**
- **Alt+X** (4133-4136行)：现金支付快速结算
- **Alt+W** (4137-4140行)：微信支付快速结算
- **Alt+Z** (4141-4144行)：支付宝支付快速结算
- **Alt+P** (4145-4148行)：POS支付快速结算
- **Shift+Space** (4149-4152行)：组合支付快速结算

#### 7.2 操作快捷键
**功能特性：**
- **Ctrl+Tab** (4153-4157行)：收银/退货模式切换
- **Ctrl+Q** (4158-4161行)：清空收银台商品列表
- **Ctrl+L** (4162-4165行)：显示上一单
- **Ctrl+B** (4166-4173行)：添加订单备注
- **Ctrl+Y** (4174行)：修改应收金额

### 8. 退货功能

#### 8.1 退货基础功能
**代码位置：** `src/components/pc_final_pay.vue` (4906-4924行)
**功能特性：**
- **退货单据处理** (4911-4913行)：退货单号和退款指纹设置
- **退货结算** (4914-4923行)：调用退货结算接口
- **退货完成** (4925-4948行)：退货完成后的处理流程

#### 8.2 退货验证
**功能特性：**
- 退货商品验证
- 退货数量限制
- 退货权限检查

### 9. 库存和拆包功能

#### 9.1 库存检查
**代码位置：** `src/common/service/saleService.js` (590-629行)
**功能特性：**
- **库存充足性检查** (608-618行)：检查商品库存是否足够销售
- **无库存销售** (621-628行)：支持无库存销售的设置
- **库存不足处理** (609-611行)：识别库存不足的商品

#### 9.2 自动拆包功能
**代码位置：** `src/common/service/saleService.js` (624-625行)
**功能特性：**
- **拆包设置** (624-625行)：支持自动拆包功能
- **拆包处理** ：当库存不足时自动拆包处理
- **拆包记录** ：记录拆包操作历史

### 10. 数据计算和处理

#### 10.1 价格计算逻辑
**代码位置：** `src/page/pc/pay.vue` (5073-5203行)
**功能特性：**
- **总金额计算** (5084-5086行)：计算商品总数量、总价、优惠金额
- **会员日商品处理** (5166-5178行)：会员日商品特殊价格处理
- **整单折扣计算** (5179-5194行)：整单折扣和减价的计算逻辑
- **抹零处理** (5202-5203行)：根据抹零方式计算最终应收金额

#### 10.2 会员优惠处理
**功能特性：**
- 会员价格应用
- 会员日商品处理
- 积分计算和抵扣
- 会员等级折扣

---

## 技术实现特点

### 1. 多支付方式集成
- 支持现金、刷卡、扫码、会员卡等多种支付方式
- 组合支付灵活配置
- 支付状态实时监控

### 2. 优惠计算引擎
- 复杂的优惠计算逻辑
- 多种优惠方式组合
- 实时价格更新

### 3. 批次批号管理
- 批次商品识别和处理
- 保质期管理
- 批次切换功能

### 4. 快捷操作支持
- 丰富的快捷键系统
- 扫码枪支持
- 语音播报功能

### 5. 数据完整性保障
- 库存实时检查
- 交易数据完整记录
- 异常情况处理

---

## 功能代码引用位置总结

| 功能模块 | 主要代码位置 | 核心文件 |
|---------|-------------|----------|
| 收银台主界面 | pay.vue:107-300 | src/page/pc/pay.vue |
| 优惠折扣 | pay.vue:1158-1217 | src/page/pc/pay.vue |
| 快捷键操作 | pay.vue:4126-4175 | src/page/pc/pay.vue |
| 支付结算 | pc_final_pay.vue:3454-5513 | src/components/pc_final_pay.vue |
| 组合支付 | pc_final_pay.vue:5418-5475 | src/components/pc_final_pay.vue |
| 会员支付 | pc_final_pay.vue:5202-5234 | src/components/pc_final_pay.vue |
| 价格计算 | pay.vue:5073-5203 | src/page/pc/pay.vue |
| 库存检查 | saleService.js:590-629 | src/common/service/saleService.js |
| 退货处理 | pc_final_pay.vue:4906-4924 | src/components/pc_final_pay.vue |
| 订单管理 | pay.vue:128-257 | src/page/pc/pay.vue |

---

**分析完成时间：** 当前
**下一步：** 继续分析S1【副屏】模块
