# S1【商品】模块详细功能说明

## 当前分析模块：S1【商品】
**状态：✅ 已完成分析**

---

## 功能概述
S1【商品】模块是ZGZN POS系统的商品信息管理核心，提供完整的商品新增、编辑、批量导入、库存管理、盘点、传秤管理、商品拆包等功能。该模块支持批次批号融合、一品多码、库存调整等高级功能。

---

## 详细功能列表

### 1. 批次批号融合（含新增）

#### 1.1 商品管理基础设置
**代码位置：** doc.md (94-95行)

##### 1.1.1 商品管理-库存设置
**功能特性：**
- 库存上限和下限设置
- 库存预警机制
- 安全库存管理

##### 1.1.2 商品管理-出库策略
**功能特性：**
- 先进先出(FIFO)策略
- 批次优先级设置
- 过期商品出库处理

#### 1.2 商品信息新增功能
**代码位置：**
- `src/components/pc_add_goods.vue` (1715-1753行) - 商品基础信息
- `src/components/pc_add_goods.vue` (2076-2108行) - 商品新增逻辑

##### 1.2.1 商品信息-新增-初始
**代码位置：** `src/components/pc_add_goods.vue` (1715-1753行)
**功能特性：**
- **基础信息设置** (1717-1736行)：货号、条码、商品名称、拼音、分类、单位
- **价格信息** (1727-1729行)：售价、会员价、进货价
- **库存信息** (1730-1733行)：当前库存、库存上下限
- **供应商关联** (1737行)：供应商信息设置

##### 1.2.2 商品信息-新增-详细
**代码位置：** `src/components/pc_add_goods.vue` (1742-1752行)
**功能特性：**
- **传秤设置** (1742-1745行)：是否传秤、传秤模式、皮重
- **批次信息** (1748-1749行)：生产日期、保质期天数
- **多码管理** (1750-1751行)：一品多码商品、扩展条码
- **规格管理** (1752行)：商品规格信息

##### 1.2.3 商品信息-选择分类
**功能特性：**
- 分类树形结构选择
- 新增分类功能
- 分类层级管理

##### 1.2.4 商品信息-选择单位
**功能特性：**
- 单位列表选择
- 新增单位功能
- 单位换算设置

#### 1.3 一品多码功能
**代码位置：** `src/common/service/goodService.js` (2929-2973行)

##### 1.3.1 商品信息-开启一品多码
**功能特性：**
- 一品多码开关设置
- 多条码管理界面

###### 1.3.1.1 一品多码-条码
**代码位置：** `src/common/service/goodService.js` (2957-2962行)
**功能特性：**
- **条码录入** (2957-2962行)：多条码输入和验证
- **重复检查** (2959-2961行)：条码重复性验证
- 条码格式验证

###### 1.3.1.2 一品多码-码重复
**代码位置：** `src/common/service/goodService.js` (2959-2961行)
**功能特性：**
- 条码重复检测机制
- 重复提示和处理

###### 1.3.1.3 一品多码-系统生成
**功能特性：**
- 系统自动生成条码
- 条码规则配置

###### 1.3.1.4 一品多码-自定义
**功能特性：**
- 用户自定义条码
- 条码格式自由设置

##### 1.3.2 商品信息-关闭或删除一品多码
**代码位置：** `src/common/service/goodService.js` (2941-2948行)

###### 1.3.2.1 一品多码-关闭提示新增
**功能特性：**
- 关闭一品多码功能提示
- 数据保留策略

###### 1.3.2.2 一品多码-删除提示新增
**功能特性：**
- 删除多码数据确认
- 不可恢复警告

#### 1.4 商品信息编辑功能

##### 1.4.1 商品信息-编辑-初始
**功能特性：**
- 商品信息回显
- 编辑权限验证

##### 1.4.2 商品信息-编辑-详细
**功能特性：**
- 详细信息修改
- 变更历史记录

##### 1.4.3 商品信息-打印提示
**功能特性：**
- 条码打印提示
- 标价签打印选项

##### 1.4.4 商品信息-编辑-改价提示
**功能特性：**
- 价格变动确认
- 影响范围提示

#### 1.5 库存调整功能

##### 1.5.1 商品信息-调整库存-盈
**功能特性：**
- 库存盈余调整
- 盈余原因记录

###### 1.5.1.1 调整库存-盈-自动分配
**功能特性：**
- 库存自动分配到批次
- 分配规则设置

###### 1.5.1.2 调整库存-盈-新增批次号
**功能特性：**
- 新批次创建
- 批次信息录入

###### 1.5.1.3 调整库存-盈-调整库存
**功能特性：**
- 库存数量调整
- 调整原因记录

##### 1.5.2 商品信息-调整库存-亏
**功能特性：**
- 库存亏损调整
- 亏损原因分析

###### 1.5.2.1 商品信息-调整库存-亏
**功能特性：**
- 亏损库存处理
- 批次选择扣减

###### 1.5.2.2 商品信息-调整库存-亏-凭证
**功能特性：**
- 调整凭证生成
- 审批流程

##### 1.5.3 商品信息-库存分布
**功能特性：**
- 批次库存分布显示
- 库存明细查询

### 2. 商品管理主界面功能

#### 2.1 商品管理（有数据）
**代码位置：** `src/page/pc/goods.vue` (1867-2409行)
**功能特性：**
- **商品列表展示** (1894-1905行)：分页显示商品信息
- **搜索功能** (1896行)：关键词搜索商品
- **分类筛选** (1900-1905行)：按分类查看商品

#### 2.2 商品管理（查看异常）
**功能特性：**
- 异常商品识别
- 异常类型分类
- 异常处理建议

#### 2.3 商品管理（批量设置）
**代码位置：** `src/page/pc/goods.vue` (2355-2406行)
**功能特性：**
- **批量设置供应商** (2356-2368行)：批量关联供应商
- **批量设置库存** (2372-2406行)：批量调整库存数量
- **批量修改分类** ：批量更改商品分类

#### 2.4 商品管理（批量设置详细）
**功能特性：**
- 详细的批量操作选项
- 操作预览和确认

#### 2.5 商品管理（筛选）
**功能特性：**
- 多条件筛选
- 高级筛选选项
- 筛选结果保存

#### 2.6 商品管理（排序）
**功能特性：**
- 多字段排序
- 自定义排序规则
- 排序状态保存

#### 2.7 商品管理（批量条码/单位/）
**功能特性：**
- 批量打印条码
- 批量修改单位
- 批量操作工具

### 3. 商品批量导入功能
**代码位置：** `src/common/service/goodService.js` (1292-1315行)

#### 3.1 商品管理（主弹窗）
**功能特性：**
- 导入操作入口
- 模板下载功能

#### 3.2 商品管理（上传文件导入中）
**功能特性：**
- 文件上传进度
- 导入状态显示

#### 3.3 商品管理（Excel 导入模板）
**代码位置：** `src/common/service/goodService.js` (1299-1314行)
**功能特性：**
- **模板结构验证** (1306-1310行)：检查模板格式
- **字段映射** (1299行)：标准字段对应关系
- **数据预处理** (1312-1314行)：导入前数据检查

#### 3.4 商品管理（批量导入成功）
**功能特性：**
- 导入结果统计
- 成功商品列表
- 操作日志记录

#### 3.5 商品管理（批量导入失败）
**功能特性：**
- 失败原因分析
- 错误数据导出
- 修正建议提供

### 4. 商品拆包功能
**代码位置：** `src/common/service/saleService.js` (776-813行)

#### 4.1 商品拆包（空状态）
**功能特性：**
- 拆包功能引导
- 操作说明展示

#### 4.2 商品拆包（新增关系）
**功能特性：**
- 拆包关系建立
- 主商品和子商品关联

#### 4.3 商品拆包（编辑关系）
**功能特性：**
- 拆包关系修改
- 拆包比例调整

#### 4.4 商品拆包（有数据）
**功能特性：**
- 拆包关系列表
- 拆包历史记录

#### 4.5 商品拆包（拆包记录）
**代码位置：** `src/common/service/saleService.js` (782-813行)
**功能特性：**
- **拆包盘点单生成** (782-813行)：自动生成盘点单记录拆包操作
- **库存自动调整** (791-797行)：拆包后自动调整相关商品库存
- **拆包原因记录** (806行)：记录拆包操作原因

### 5. 库存盘点功能
**代码位置：** `src/common/service/inventoryService.js` (5-684行)

#### 5.1 库存盘点（空状态）
**功能特性：**
- 盘点操作引导
- 盘点流程说明

#### 5.2 库存盘点（选择商品）
**代码位置：** `src/page/pc/pc_add_inventory_list.vue` (124-270行)
**功能特性：**
- 商品选择界面
- 批量选择功能
- 分类筛选

#### 5.3 库存盘点（已选商品）
**功能特性：**
- 已选商品列表
- 盘点数量录入
- 实际库存对比

#### 5.4 库存盘点（已选品-切换码）⭐
**功能特性：**
- 商品条码切换
- 一品多码盘点

#### 5.5 库存盘点（调整库存-盈）
**代码位置：** `src/common/service/inventoryService.js` (513-550行)
**功能特性：**
- **盘点类型管理** (515-530行)：支持多种盘点类型(新增、批量导入、盘点、抵消)
- **盘点记录查询** (531-550行)：按类型和时间范围查询盘点记录
- **库存调整处理** ：盘盈库存的处理和记录

### 6. 传秤管理功能
**代码位置：** `src/page/pc/scales_manage.vue` (514-1150行)

#### 6.1 传秤商品设置
**代码位置：** `src/components/pc_add_goods.vue` (1466-1498行)
**功能特性：**
- **传秤开关** (1470-1472行)：是否传秤商品设置
- **传秤模式** (1476-1480行)：称重、计件、定重模式选择
- **皮重设置** (1494-1496行)：传秤商品皮重配置

#### 6.2 传秤管理界面
**代码位置：** `src/page/pc/scales_manage.vue` (520-542行)
**功能特性：**
- **传秤说明** (520-537行)：传秤操作指南和注意事项
- **传秤操作** (540行)：传秤数据发送
- **保存操作** (541行)：传秤商品信息保存

#### 6.3 传秤数据处理
**代码位置：** `src/page/pc/scales_manage.vue` (1112-1150行)
**功能特性：**
- **传秤数据组装** (1114-1126行)：组装传秤所需的商品数据
- **热键配置** (1131-1141行)：条码秤热键配置
- **网络通信** (1149行)：向条码秤发送商品信息

#### 6.4 收银台传秤商品处理
**代码位置：** `src/page/pc/pay.vue` (4530-4575行)
**功能特性：**
- **传秤条码识别** (4532行)：识别20开头的传秤商品条码
- **重量价格解析** (4534-4538行)：解析条码中的重量和价格信息
- **传秤商品添加** (4549-4563行)：自动计算数量并添加到购物清单

### 7. 商品验证和检查功能

#### 7.1 商品提交前验证
**代码位置：** `src/components/pc_add_goods.vue` (2042-2075行)
**功能特性：**
- **重复性检查** (2043-2047行)：条码和名称重复验证
- **必填字段验证** (2048-2051行)：商品名称等必填项检查
- **传秤商品验证** (2052-2065行)：传秤商品特殊规则验证
- **库存范围验证** (2066-2073行)：库存上下限和数值范围检查

#### 7.2 条码自动生成
**代码位置：** `src/components/pc_add_goods.vue` (2030-2037行)
**功能特性：**
- **传秤条码生成** (2031-2034行)：传秤商品自动生成条码
- **条码格式验证** (2060-2064行)：20开头7位数字格式验证

---

## 技术实现特点

### 1. 批次批号管理
- 完整的生产日期和保质期追溯
- 批次库存分配和调整
- 过期预警和批次管理

### 2. 一品多码支持
- 灵活的多条码管理
- 条码重复检测机制
- 系统生成和自定义条码

### 3. 传秤商品集成
- 与条码秤设备通信
- 传秤数据实时处理
- 称重商品特殊处理流程

### 4. 库存管理完整性
- 多维度库存调整
- 盘点流程标准化
- 库存变动全程追溯

### 5. 批量操作优化
- 高效的批量导入处理
- 批量设置和修改功能
- 数据验证和错误处理

---

## 功能代码引用位置总结

| 功能模块 | 主要代码位置 | 核心文件 |
|---------|-------------|----------|
| 商品新增 | pc_add_goods.vue:2076-2108 | src/components/pc_add_goods.vue |
| 一品多码 | goodService.js:2929-2973 | src/common/service/goodService.js |
| 批量导入 | goodService.js:1292-1315 | src/common/service/goodService.js |
| 商品拆包 | saleService.js:776-813 | src/common/service/saleService.js |
| 库存盘点 | inventoryService.js:513-550 | src/common/service/inventoryService.js |
| 传秤管理 | scales_manage.vue:514-1150 | src/page/pc/scales_manage.vue |
| 商品管理 | goods.vue:1867-2409 | src/page/pc/goods.vue |
| 传秤设置 | pc_add_goods.vue:1466-1498 | src/components/pc_add_goods.vue |

---

**分析完成时间：** 当前
**下一步：** 继续分析S1【收银台】模块
