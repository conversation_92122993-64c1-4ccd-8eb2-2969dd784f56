# S2【会员】模块详细功能说明

## 当前分析模块：S2【会员】
**状态：✅ 已完成分析**

---

## 功能概述
S2【会员】模块是ZGZN POS系统的核心客户关系管理模块，提供完整的会员管理、充值、次卡、积分、小程序等功能。该模块支持会员信息管理、储值消费、积分兑换、次卡管理等多种会员服务功能，为商家提供全面的会员营销解决方案。

---

## 详细功能列表

### 1. 会员管理主页面

#### 1.1 会员管理基础功能
**代码位置：** `src/page/pc/member.vue` (1-300行)
**功能特性：**
- **会员列表展示** (138-147行)：会员信息表格展示，支持分页显示
- **搜索功能** (78-111行)：会员姓名、手机号搜索
- **筛选排序** (235-268行)：按充值金额、积分等条件筛选和排序

#### 1.2 会员管理（空状态）
**功能特性：**
- 空状态页面设计
- 新增会员引导
- 批量导入入口

#### 1.3 会员管理（新增会员）
**代码位置：** `src/components/pc_add_member.vue` (898-928行)
**功能特性：**
- **基础信息录入** ：姓名、手机号、生日、性别等
- **会员等级设置** ：会员折扣等级配置
- **初始余额设置** ：新会员开卡充值
- **积分初始化** ：初始积分设置

#### 1.4 会员管理（查看会员）
**代码位置：** `src/components/pc_add_member.vue` (898-928行)
**功能特性：**
- **会员详情展示** ：完整会员信息查看
- **余额显示** (898-900行)：当前可用余额
- **积分显示** (902-904行)：当前可用积分
- **次卡统计** (916-917行)：持有次卡数量统计

#### 1.5 会员管理（批量设置）
**功能特性：**
- 批量会员折扣设置
- 批量会员等级调整
- 批量会员信息更新

#### 1.6 会员管理（批量改折扣）
**功能特性：**
- 选择多个会员
- 统一折扣率设置
- 批量操作确认

#### 1.7 会员（批量导入）
**功能特性：**
- **Excel文件上传** ：支持批量会员信息导入
- **导入进度显示** ：实时显示导入进度
- **导入结果反馈** ：成功/失败统计
- **错误信息导出** ：失败记录可导出修正

### 2. 会员充值管理

#### 2.1 会员充值入口
**代码位置：** `src/page/pc/pay.vue` (112-113行)
**功能特性：**
- 收银台快速充值入口
- 会员充值弹窗组件

#### 2.2 会员充值-主页面
**代码位置：** `src/components/pc_member_recharge.vue` (470-633行)
**功能特性：**
- **会员信息显示** (495-502行)：会员基础信息和当前余额
- **充值金额输入** (513-523行)：充值金额输入和验证（最大999999.99）
- **赠送金额设置** (524-528行)：充值赠送金额配置

#### 2.3 会员充值-修改赠送
**功能特性：**
- 赠送比例设置
- 赠送金额计算
- 赠送规则配置

#### 2.4 会员充值-充值结算
**代码位置：** `src/components/pc_member_recharge.vue` (573-618行)
**功能特性：**
- **支付方式选择** (604行)：默认现金支付
- **结算参数设置** (594-618行)：充值金额、赠送金额、会员信息等
- **支付流程处理** (617行)：调用支付结算界面

#### 2.5 会员充值-充值记录
**代码位置：** `src/components/pc_member_recharge.vue` (472-511行)
**功能特性：**
- **充值历史查询** (477-499行)：分页查询会员充值记录
- **记录详情展示** (496-498行)：充值时间、金额、操作员等信息
- **撤销功能** (491-493行)：最后一笔充值可撤销

#### 2.6 会员充值-消费记录
**功能特性：**
- 会员消费历史记录
- 消费金额统计
- 消费时间记录

#### 2.7 会员充值-积分兑换 ⭐
**代码位置：** `src/components/pc_member_points_exchange.vue` (92-136行)
**功能特性：**
- **兑换商品选择** (116行)：积分兑换商品名称显示
- **兑换数量设置** (119-128行)：兑换数量输入（1-99999范围）
- **小票打印设置** (130-134行)：兑换完成是否打印小票
- **积分扣除计算** ：自动计算所需积分并扣除

#### 2.8 会员充值-余额变动通知
**功能特性：**
- 余额变动短信通知
- 微信推送通知
- 余额预警提醒

### 3. 会员次卡管理

#### 3.1 会员次卡入口
**代码位置：** `src/page/pc/pay.vue` (120-121行)
**功能特性：**
- 收银台次卡管理入口
- 次卡弹窗组件

#### 3.2 会员次卡-购买次卡
**代码位置：** `src/components/pc_vip_times_card.vue` (276-292行)
**功能特性：**
- **次卡搜索功能** (288-292行)：按次卡名称搜索
- **次卡列表展示** (294-308行)：可购买次卡列表
- **批量选择购买** (726-728行)：支持同时购买多张次卡

#### 3.3 会员次卡-购买次卡结算
**代码位置：** `src/components/pc_vip_times_card.vue` (725-779行)
**功能特性：**
- **金额计算** (721-724行)：次卡总价格计算和格式化
- **支付参数设置** (744-776行)：
  - 会员支付方式（acctsId: 8）
  - 次卡商品列表
  - 支付金额设置
- **支付流程处理** (777-779行)：调用支付结算界面

#### 3.4 会员次卡-查看持有次卡 ⭐
**代码位置：** `src/components/pc_add_member.vue` (916-919行)
**功能特性：**
- **次卡统计显示** (917行)：持有次卡总数和剩余次数
- **次卡详情查看** (919行)：查看次卡详细信息

#### 3.5 会员次卡-查看次卡详情
**功能特性：**
- 次卡使用历史
- 剩余次数显示
- 次卡有效期信息

#### 3.6 会员次卡-消费次卡
**代码位置：** `src/components/pc_vip_times_card.vue` (781-819行)
**功能特性：**
- **次卡消费选择** (781-805行)：选择要消费的次卡
- **消费次数设置** (797-804行)：设置本次消费次数
- **最大可用次数控制** (799-803行)：自动计算最大可消费次数
- **消费确认处理** (817-819行)：次卡消费逻辑处理

### 4. 会员其他设置

#### 4.1 积分规则
**功能特性：**
- 消费积分比例设置
- 积分兑换比例设置
- 积分有效期设置
- 积分清零规则

#### 4.2 会员等级设置
**功能特性：**
- 会员等级定义
- 等级折扣设置
- 等级升级条件
- 等级权益配置

#### 4.3 储值优惠设置
**功能特性：**
- 充值赠送比例
- 充值阶梯优惠
- 首次充值优惠
- 节日充值活动

### 5. 会员支付集成

#### 5.1 会员储值支付
**代码位置：** `src/common/service/shiftHistoryService.js` (545-589行)
**功能特性：**
- **会员充值统计** (546-589行)：
  - 会员卡充值统计（type: 1）
  - 充值撤销统计（type: 4）
  - 退款到会员卡统计（type: 3, originId: 4）
- **充值金额汇总** (564-571行)：总充值金额和笔数统计

#### 5.2 收银台会员功能
**代码位置：** `src/page/pc/pay.vue` (116-121行)
**功能特性：**
- **积分兑换入口** (116-117行)：收银台积分兑换商品
- **新增会员入口** (118-119行)：收银台快速新增会员
- **次卡管理入口** (120-121行)：收银台次卡购买和消费

#### 5.3 会员价格体系
**功能特性：**
- 会员价格显示
- 会员折扣计算
- 等级折扣应用
- 优惠叠加规则

---

## 会员数据管理

### 1. 会员信息存储
- 会员基础信息（姓名、手机、生日等）
- 会员余额和积分
- 会员等级和折扣
- 会员消费历史

### 2. 会员数据同步
- 云端数据同步
- 离线数据缓存
- 数据冲突解决
- 实时状态更新

### 3. 会员隐私保护
- 会员信息加密
- 访问权限控制
- 操作日志记录
- 数据备份恢复

---

## 技术实现特点

### 1. 组件化设计
- 会员充值独立组件
- 次卡管理独立组件
- 积分兑换独立组件
- 会员新增独立组件

### 2. 状态管理
- Vuex统一状态管理
- 会员信息全局共享
- 实时数据更新
- 状态持久化存储

### 3. 网络处理
- 在线/离线状态检测
- 网络异常处理
- 数据同步机制
- 错误重试机制

---

## 功能代码引用位置总结

| 功能模块 | 主要文件位置 | 关键代码行数 |
|---------|-------------|-------------|
| 会员管理主页 | src/page/pc/member.vue | 1-300行 |
| 会员新增编辑 | src/components/pc_add_member.vue | 898-928行 |
| 会员充值管理 | src/components/pc_member_recharge.vue | 470-633行 |
| 积分兑换功能 | src/components/pc_member_points_exchange.vue | 92-136行 |
| 次卡管理功能 | src/components/pc_vip_times_card.vue | 268-819行 |
| 充值统计报表 | src/common/service/shiftHistoryService.js | 545-589行 |
| 收银台会员入口 | src/page/pc/pay.vue | 112-121行 |

---

## 备注
会员模块是POS系统的重要营销功能，需要网络连接进行云端数据同步。该模块提供了完整的会员生命周期管理，从会员注册、充值、消费到积分兑换等全流程服务，是提升客户粘性和促进复购的核心工具。
