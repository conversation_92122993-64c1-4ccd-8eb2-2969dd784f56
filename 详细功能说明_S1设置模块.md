# S1【设置】模块详细功能说明

## 当前分析模块：S1【设置】
**状态：✅ 已完成分析**

---

## 功能概述
S1【设置】模块是ZGZN POS系统的核心配置管理中心，涵盖店铺信息、配件设置、账号安全、系统配置等全方位设置功能。该模块提供了完整的系统个性化配置和管理能力。

---

## 详细功能列表

### 1. 店铺信息设置
**代码位置：**
- `src/page/pc/setting.vue` (1001-1089行) - 店铺基本信息
- `src/page/pc/setting.vue` (5358-5370行) - 版本信息显示

#### 1.1 基本信息
**功能特性：**
- **店铺名称设置** (1001-1010行)：店铺名称编辑和保存
- **营业执照编号** (1011-1020行)：营业执照信息维护
- **店铺地址** (1021-1030行)：详细地址信息管理
- **联系电话** (1031-1040行)：联系方式设置
- **经营者信息** (1041-1050行)：经营者姓名等信息

#### 1.2 版本信息
**代码位置：** `src/page/pc/setting.vue` (5358-5370行)
**功能特性：**
- **版本显示** (5359行)：旗舰版/专业版/简易版标识
- **授权期限** (5360-5361行)：到期日期显示
- **立即续费** (5363-5369行)：续费入口和版本比较

### 2. 配件设置
**代码位置：**
- `src/components/pc_print_setting.vue` (410-1729行) - 配件设置组件
- `src/page/pc/setting.vue` (1613-1686行) - 配件设置保存逻辑

#### 2.1 小票打印设置
**代码位置：** `src/components/pc_print_setting.vue` (1620-1625行)
**功能特性：**
- **打印机选择** (1620行)：小票打印机设备选择
- **字号设置** (1621行)：小票正文字号配置
- **行间距设置** (1622行)：小票行间距调整
- **打印规格** (1623行)：小票打印规格选择
- **备注内容** (1639行)：小票备注内容设置
- **会员信息打印** (1641行)：会员信息显示控制

#### 2.2 条码打印设置
**代码位置：** `src/components/pc_print_setting.vue` (1617-1642行)
**功能特性：**
- **条码打印机** (1617行)：条码打印设备选择
- **条码纸规格** (1624行)：条码纸张规格设置
- **标价签打印** (1618行)：标价签打印设备配置
- **吊牌打印** (1618行)：吊牌打印设备配置
- **打印内容** (1642行)：条码需要打印的信息设置

#### 2.3 钱箱设置
**代码位置：** `src/components/pc_print_setting.vue` (1626-1631行)
**功能特性：**
- **钱箱启用** (1631行)：是否启用钱箱功能
- **钱箱端口** (1627-1629行)：钱箱连接端口配置(COM1-COM6)
- **钱箱类型** (1003行)：钱箱设备类型选择

#### 2.4 电子秤设置
**代码位置：** `src/components/pc_print_setting.vue` (1225-1237行)
**功能特性：**
- **电子秤启用** (1226行)：是否启用电子秤功能
- **端口设置** (1227行)：电子秤连接端口(默认COM2)
- **秤型选择** (1228行)：电子秤类型(如HongHaiACS)
- **首次使用检测** (1229行)：首次打开称重商品检测
- **连接测试** (1692-1726行)：电子秤连接测试功能

#### 2.5 副屏广告设置
**代码位置：** `src/components/pc_print_setting.vue` (425-435行)
**功能特性：**
- **广告启用开关** (427-432行)：启用副屏广告功能
- **广告刷新** (434行)：手动刷新广告内容
- **广告切换时间** (986行,1262-1264行)：图片广告切换间隔设置
- **广告管理** (1680-1682行)：广告内容管理入口

#### 2.6 其他配件设置
**功能特性：**
- **语音播报** (1615行)：语音播报开关设置
- **音量控制** (992行)：音量强度调节
- **扫码提示音** (993行)：扫码提示音开关
- **负利润检测** (1616行)：负利润提醒设置
- **默认支付方式** (994-999行)：收银台默认支付方式选择

### 3. 账号安全
**代码位置：**
- `src/page/pc/setting.vue` (2608-2690行) - 修改密码界面
- `src/page/pc/setting.vue` (5371-5463行) - 密码修改逻辑

#### 3.1 修改密码
**功能特性：**
- **原密码验证** (2615-2621行,5392-5399行)：原密码输入和验证
- **新密码设置** (2625-2640行,5400-5403行)：新密码输入(8-20字符，数字字母组合)
- **确认密码** (2641-2655行,5404-5418行)：新密码确认输入
- **密码强度检测** (5371-5375行)：密码格式验证
- **在线修改** (5421-5462行)：联网状态下密码修改

#### 3.2 清空数据
**代码位置：** `src/page/pc/setting.vue` (2261-2591行)
**功能特性：**
- **数据清空确认** (2262-2345行)：清空数据二次确认弹窗
- **分类数据清空** (2475-2498行)：按单据类型清空(销售/进货/盘点)
- **批量选择删除** (2496-2591行)：选择性删除单据数据
- **日期范围选择** (2488-2490行)：按时间范围清空数据

#### 3.3 注销账号
**代码位置：** `src/page/pc/setting.vue` (2248-2260行)
**功能特性：**
- **注销确认** (2251行)：注销账号操作入口
- **权限清空说明** (2257行)：清空产品使用权限及业务数据说明
- **不可恢复警告** (2257行)：数据清空后无法恢复的风险提示

### 4. 收银台设置
**代码位置：**
- `src/page/pc/setting.vue` (5333-5349行) - 默认支付方式
- `src/components/pc_print_setting.vue` (994-999行) - 支付选项

#### 4.1 默认支付方式
**功能特性：**
- **现金支付** (996行)：现金支付选项
- **线下支付** (997行)：线下支付选项  
- **扫码支付** (998行)：扫码支付选项
- **自动选择** (5333-5349行)：根据配置自动选择默认支付方式

#### 4.2 收银辅助功能
**功能特性：**
- **抹零设置** (1614行)：小额抹零功能配置
- **热销商品显示** (5347-5348行)：收银台热销商品展示开关
- **称重商品支持** (1611行)：称重商品在收银台的显示设置

### 5. 系统配置
**代码位置：**
- `src/page/pc/setting.vue` (5350-5356行) - 微店设置
- `src/page/pc/setting.vue` (2592-2607行) - 功能模块迁移提示

#### 5.1 高级功能配置
**功能特性：**
- **微店设置** (5350-5356行)：微店功能开关配置
- **条码秤功能** (990行)：条码秤开关设置
- **库存显示** (991行)：库存信息显示设置

#### 5.2 功能模块整合提示
**代码位置：** `src/page/pc/setting.vue` (2592-2607行)
**功能特性：**
- **会员设置迁移** (2593-2599行)：会员设置功能已迁移到会员模块提示
- **消息设置迁移** (2601-2607行)：消息设置功能已迁移到会员模块提示
- **界面引导图片** (2598行,2606行)：功能位置引导图片展示

---

## 技术实现特点

### 1. 配置存储机制
- 使用`settingService`进行本地配置存储
- 支持键值对形式的配置管理
- 配置实时生效和持久化存储

### 2. 硬件设备集成
- 支持多种打印机设备自动识别
- 电子秤、钱箱等硬件设备通信
- COM端口配置和设备连接测试

### 3. 安全性保障
- 密码MD5加密存储和传输
- 在线验证和本地验证双重保障
- 操作日志记录和风险提示

### 4. 用户体验优化
- 实时预览和测试功能
- 配置向导和操作引导
- 错误提示和操作确认机制

---

## 功能代码引用位置总结

| 功能模块 | 主要代码位置 | 核心文件 |
|---------|-------------|----------|
| 店铺信息 | setting.vue:1001-1089 | src/page/pc/setting.vue |
| 配件设置 | pc_print_setting.vue:410-1729 | src/components/pc_print_setting.vue |
| 账号安全 | setting.vue:2608-2690,5371-5463 | src/page/pc/setting.vue |
| 收银设置 | setting.vue:5333-5349 | src/page/pc/setting.vue |
| 系统配置 | setting.vue:5350-5356 | src/page/pc/setting.vue |

---

**分析完成时间：** 当前
**下一步：** 继续分析S1【进货/退货】模块
