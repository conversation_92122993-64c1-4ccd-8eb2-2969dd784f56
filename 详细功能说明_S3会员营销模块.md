# S3【会员营销】模块详细功能说明

## 当前分析模块：S3【会员营销】
**状态：✅ 已完成分析**

---

## 功能概述
S3【会员营销】模块是ZGZN POS系统的核心营销推广模块，主要提供短信群发功能，为商家提供专业的会员营销和客户触达解决方案。该模块支持短信模板管理、批量发送、发送记录追踪等功能，帮助商家进行有效的会员营销推广。

---

## 详细功能列表

### 1. 短信群发功能

#### 1.1 短信群发主界面
**代码位置：** `src/page/pc/short_message_mass.vue` (245-889行)
**功能特性：**
- **手机号输入区域** (288-298行)：
  - 支持手机号批量输入（逗号分隔）
  - 支持复制粘贴批量导入
  - 最大支持10000字符长度
  - 自动格式化手机号（formatPhone功能）
- **会员导入功能** (284-286行)：一键导入会员手机号
- **短信内容编辑** ：支持短信内容编辑和字数统计

#### 1.2 短信发送处理
**代码位置：** `src/page/pc/short_message_mass.vue` (791-842行)
**功能特性：**
- **发送前验证** (843-873行)：
  - 手机号格式验证（11位手机号格式检查）
  - 短信内容非空验证
  - 短信签名验证
  - 发送量限制验证（单次最多500条）
- **发送接口调用** (813-841行)：
  - API接口：`this.$rest.sendMessage`
  - 发送参数包含：签名、内容、手机号列表、系统信息
  - 超时设置：60秒
  - 发送结果处理和反馈

#### 1.3 短信模板管理
**代码位置：** `src/page/pc/short_message_mass.vue` (461-463行)
**功能特性：**
- 短信模板保存功能
- 常用模板快速选择
- 模板内容管理

#### 1.4 短信发送规则和限制
**代码位置：** `src/page/pc/short_message_mass.vue` (464-485行)
**发送须知：**
- **发送时间限制** (467行)：9:00-18:00时间段
- **自动添加退订** (468行)：系统自动添加"拒收请回复R"
- **内容建议** (469行)：建议以"尊敬的会员"开头
- **合规要求** (470-485行)：严格遵守相关法律法规
- **法律责任** (472行)：使用者承担违规内容责任

### 2. 短信服务管理

#### 2.1 短信服务购买
**代码位置：** `src/page/pc/short_message.vue` (274-287行)
**功能特性：**
- **短信套餐选择** (274-287行)：多种短信条数套餐
- **价格显示** (283-286行)：套餐价格和条数展示
- **购买流程** ：选择套餐-结算-支付完成

#### 2.2 短信余额管理
**功能特性：**
- 短信余额查询
- 余额不足提醒
- 充值引导功能

### 3. 短信合规管理

#### 3.1 短信服务用户告知书
**代码位置：** `src/components/pc_sms_contract.vue` (182-559行)
**功能特性：**
- **法律告知** (194-207行)：短信服务相关法律法规告知
- **用户协议** (510-547行)：用户同意短信风险授权协议
- **合规检查** (455-509行)：查询短信发送风险授权记录

#### 3.2 短信签名管理
**代码位置：** `src/config/rest.js` (297-300行)
**API接口：**
- **申请签名** (298行)：`applySign` - 短信签名审核申请
- **获取签名** (300行)：`selectSign` - 获取短信签名列表
- **签名状态** ：审核中、已通过、已拒绝等状态管理

#### 3.3 短信内容审核
**代码位置：** `src/page/pc/short_message_mass.vue` (838-840行)
**功能特性：**
- 发送前内容合规检查
- 违规内容自动拦截
- 违规提示和内容标识

### 4. 短信记录管理

#### 4.1 短信发送记录
**代码位置：** `src/components/Header.vue` (1631-1635行)
**功能特性：**
- 短信发送历史记录查询
- 发送状态跟踪
- 发送详情查看

#### 4.2 短信购买记录
**代码位置：** `src/components/Header.vue` (1636-1638行)
**功能特性：**
- 短信套餐购买历史
- 购买时间和金额记录
- 余额变动记录

#### 4.3 短信群发记录
**代码位置：** `src/components/Header.vue` (1639-1643行)
**功能特性：**
- 群发任务历史记录
- 群发成功率统计
- 群发详情查看

### 5. 会员通知设置

#### 5.1 自动短信通知
**代码位置：** `src/components/pc_sms_contract.vue` (306-310行)
**通知类型：**
- **账户变动通知** (307行)：会员消费、充值时自动发送短信
- **积分兑换通知** (308行)：积分兑换时自动通知
- **次卡消息通知** (309行)：次卡购买、消费时通知

#### 5.2 通知开关管理
**代码位置：** `src/components/pc_sms_contract.vue` (434-453行)
**功能特性：**
- **通知设置查询** (442-453行)：获取会员短信发送设置开关状态
- **个性化配置** ：不同类型通知独立开关控制
- **会员授权** ：基于会员授权的通知发送

### 6. 短信API集成

#### 6.1 短信接口配置
**代码位置：** `src/config/rest.js` (284-296行)
**API端点：**
- **发送短信** (285行)：`sendMessage` - 短信群发接口
- **发送记录** (286行)：`smsList` - 短信发送记录查询
- **发送详情** (287行)：`smsListDetails` - 短信发送详情
- **确认状态** (291行)：`getOne` - 短信告知书确认状态
- **配置获取** (293行)：`getValues` - 短信相关配置获取
- **协议同意** (295行)：`agreeShort` - 同意短信告知书

#### 6.2 网络状态检测
**代码位置：** `src/page/pc/short_message_mass.vue` (793-796行)
**功能特性：**
- 发送前网络连接检测
- 离线状态提示
- 网络异常处理

---

## 短信营销策略支持

### 1. 精准营销
- 基于会员标签的精准推送
- 会员生日、节日自动营销
- 消费行为触发营销

### 2. 营销效果跟踪
- 发送成功率统计
- 营销效果分析
- ROI计算支持

### 3. 合规营销
- 严格的内容审核机制
- 发送时间限制
- 退订机制支持

---

## 技术实现特点

### 1. 安全合规
- 严格的内容过滤机制
- 用户授权管理
- 法律法规遵循

### 2. 稳定可靠
- 专用短信通道
- 发送状态监控
- 失败重试机制

### 3. 用户体验
- 简洁的操作界面
- 批量操作支持
- 实时反馈机制

---

## 功能代码引用位置总结

| 功能模块 | 主要文件位置 | 关键代码行数 |
|---------|-------------|-------------|
| 短信群发主界面 | src/page/pc/short_message_mass.vue | 245-889行 |
| 短信发送处理 | src/page/pc/short_message_mass.vue | 791-873行 |
| 短信服务购买 | src/page/pc/short_message.vue | 274-287行 |
| 短信合规管理 | src/components/pc_sms_contract.vue | 182-559行 |
| 短信记录管理 | src/components/Header.vue | 1631-1643行 |
| 短信API配置 | src/config/rest.js | 284-296行 |
| 会员通知设置 | src/components/pc_sms_contract.vue | 306-310行, 434-453行 |

---

## 备注
会员营销模块需要网络连接和短信服务授权才能正常使用。该模块严格遵守相关法律法规，提供合规的短信营销服务，是提升客户粘性和促进销售的重要工具。使用前需要完成短信服务用户告知书的签署和短信签名的申请审核。