# S1【小票】模块详细功能说明

## 当前分析模块：S1【小票】
**状态：✅ 已完成分析**

---

## 功能概述
S1【小票】模块是ZGZN POS系统的打印输出核心模块，提供完整的小票打印、标价签打印、条码打印等功能。该模块支持多种打印机类型、打印模板定制、格式设置等高级功能，为商家提供专业的票据输出解决方案。

---

## 详细功能列表

### 1. 小票打印功能

#### 1.1 小票打印基础设置
**代码位置：**
- `src/page/pc/setting.vue` (2757-2781行) - 小票打印机选择
- `src/components/pc_print_setting.vue` (1620-1643行) - 小票打印参数设置

**功能特性：**
- **打印机选择** (2765-2778行)：支持多种小票打印机选择
- **小票规格设置** (1623行)：58mm、80mm等规格支持
- **字体设置** (1621行)：小票正文字号可调节
- **行间距设置** (1622行)：打印行间距自定义
- **打印份数设置** (1090行)：小票默认打印份数配置
- **打印驱动选择** (1080-1086行)：指令打印/驱动打印两种模式

#### 1.2 小票内容配置
**代码位置：** `src/components/pc_print_setting.vue` (1093-1102行)
**功能特性：**
- **会员信息打印** (1093-1098行)：
  - 会员名称打印开关
  - 会员手机号打印开关
  - 会员卡余额显示开关
  - 会员积分显示开关
- **Logo和二维码** (1099-1102行)：
  - 店铺Logo打印功能（建议300*100像素）
  - 商家二维码打印功能（建议400*400像素）
- **小票备注内容** (1639行)：自定义小票底部备注信息

#### 1.3 小票打印模板
**代码位置：** `src/common/posApi.js` (363-398行)
**功能特性：**
- **模板选择** (378行)：SCShoppingReceiptTemplate标准模板
- **打印参数配置** (367-398行)：
  - 列数设置（cols）
  - 打印机名称配置
  - 店铺名称显示
  - 行高设置
  - 打印模式（1001指令/1000驱动）
- **动态内容** (374-395行)：
  - 交易时间
  - 收银员信息
  - 打印时间
  - 销售单号/退货单号
  - 备注内容

#### 1.4 小票打印触发机制
**代码位置：**
- `src/components/back_money_dialog.vue` (537-588行) - 退货小票打印
- `src/common/service/settingService.js` (629-635行) - 支付完成自动打印设置

**触发条件：**
- **支付完成自动打印** (629-635行)：可设置支付完成后是否自动打印小票
- **退货小票打印** (537-588行)：退货完成后自动打印退货小票
- **会员充值小票** ：会员充值完成后打印充值小票
- **手动补打小票** ：支持历史订单小票补打功能

### 2. 标价签打印功能

#### 2.1 标价签模板设置
**代码位置：** `src/components/pc_print_setting.vue` (1113-1147行)
**功能特性：**
- **多模板支持** (1113-1147行)：
  - 模板1：完整商品信息模板
  - 模板2：简化信息模板
- **标价签内容配置** ：
  - 品牌信息（1118行）
  - 商品名称（1119行）
  - 货号信息（1120行）
  - 季节信息（1121行）
  - 成分信息（1122行）
  - 等级信息（1123行）
  - 售价显示（1125行）
  - 建议零售价（1126行）
  - 条码打印（1127行）

#### 2.2 标价签打印控制
**代码位置：** `src/components/pc_tag.vue` (369-456行)
**功能特性：**
- **批量打印** (369-412行)：支持批量选择商品打印标价签
- **打印参数配置** (400-411行)：
  - 打印机名称设置
  - 纸张宽高设置（60mm/30mm规格）
  - 横向/纵向打印方向
  - 字体大小配置
- **条码生成** (434-438行)：商品条码和货号条码自动生成
- **重复打印** (444-450行)：支持设置打印份数

### 3. 条码标签打印功能

#### 3.1 条码标签基础设置
**代码位置：** `src/components/pc_print_setting.vue` (1106-1112行)
**功能特性：**
- **条码标签内容配置** (1106-1112行)：
  - 商品名称显示开关
  - 商品条码打印开关
  - 生产日期显示开关
  - 保质期显示开关

#### 3.2 条码标签打印
**代码位置：** `src/components/pc_print_setting.vue` (1433-1490行)
**功能特性：**
- **标签尺寸设置** (1480-1490行)：
  - 40mm、60mm、64mm等多种规格支持
  - 横向/纵向打印方向控制
- **打印内容布局** (1452-1478行)：
  - 商品名称区域
  - 售价显示区域
  - 条码显示区域（60x60mm）
  - 生产日期和保质期区域

### 4. 特殊小票打印功能

#### 4.1 取件单打印
**代码位置：** `src/common/posApi.js` (900-934行)
**功能特性：**
- **取件单模板** (916-923行)：
  - 取件时间显示
  - 收银员信息
  - 打印时间
  - 商品信息列表
  - 会员名称
- **取件单打印控制** (904-934行)：自动检测打印机状态并执行打印

#### 4.2 会员相关小票
**代码位置：**
- `src/components/back_money_dialog.vue` (569-588行) - 会员退货小票
- 会员充值小票、积分兑换小票等

**功能特性：**
- 会员信息自动查询和显示
- 会员余额变动记录
- 积分变动记录
- 会员专属优惠信息

### 5. 打印队列和错误处理

#### 5.1 打印状态检测
**代码位置：** `src/common/posApi.js` (904-909行)
**功能特性：**
- **打印机状态检测** (904-909行)：自动检测小票打印机连接状态
- **错误提示机制** ：打印机异常时显示具体错误信息
- **重试机制** ：打印失败时支持重新打印

#### 5.2 打印日志记录
**功能特性：**
- 打印成功/失败日志记录
- 打印参数记录
- 错误信息详细记录

---

## 打印机驱动和协议支持

### 1. 支持的打印机类型
- **热敏小票打印机** ：58mm、80mm规格
- **标价签打印机** ：支持不干胶标签打印
- **条码打印机** ：支持一维条码打印
- **吊牌打印机** ：支持服装吊牌打印

### 2. 打印协议支持
- **ESC/POS指令集** ：标准热敏打印机协议
- **驱动打印模式** ：系统驱动方式打印
- **USB/串口/网络** ：多种连接方式支持

### 3. 纸张规格支持
- **小票纸** ：58mm、80mm宽度
- **标签纸** ：40mm、60mm、64mm等规格
- **吊牌纸** ：30mm、60mm等规格

---

## 技术实现特点

### 1. 模板化设计
- 支持多种打印模板
- 内容项目可配置开关
- 字体大小和布局可调

### 2. 硬件兼容性
- 支持多品牌打印机
- 自动检测打印机状态
- 错误处理和重试机制

### 3. 数据同步
- 打印内容实时同步业务数据
- 会员信息动态获取
- 商品信息自动更新

---

## 功能代码引用位置总结

| 功能模块 | 主要文件位置 | 关键代码行数 |
|---------|-------------|-------------|
| 小票打印API | src/common/posApi.js | 232-956行 |
| 小票打印设置 | src/components/pc_print_setting.vue | 1620-1643行 |
| 标价签打印 | src/components/pc_tag.vue | 369-456行 |
| 条码标签设置 | src/components/pc_print_setting.vue | 1106-1112行, 1433-1490行 |
| 退货小票打印 | src/components/back_money_dialog.vue | 537-588行 |
| 取件单打印 | src/common/posApi.js | 900-934行 |
| 打印配置管理 | src/common/service/settingService.js | 587-659行 |
| 小票设置界面 | src/page/pc/setting.vue | 2757-2781行 |

---

## 备注
小票打印模块是POS系统的重要输出功能，需要配合相应的硬件打印机使用。该模块提供了丰富的打印模板和格式选项，能够满足不同商家的票据打印需求。
