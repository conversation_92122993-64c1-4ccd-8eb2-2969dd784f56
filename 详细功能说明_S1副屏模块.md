# S1【副屏】模块详细功能说明

## 当前分析模块：S1【副屏】
**状态：✅ 已完成分析**

---

## 功能概述
S1【副屏】模块是ZGZN POS系统的客户显示功能模块，为顾客提供购物过程中的商品信息、价格详情、支付信息等可视化展示，提升购物体验和收银透明度。该模块支持商品列表显示、会员信息展示、广告播放等功能。

---

## 详细功能列表

### 1. 副屏显示功能

#### 1.1 副屏基础配置
**代码位置：**
- `src/store/show.js` (400-423行) - 副屏状态管理
- `src/page/pc/setting.vue` (3653-3703行) - 副屏设置配置

**功能特性：**
- **副屏启用设置** (3653-3661行)：开启/关闭副屏功能
- **客显地址配置** (3669-3682行)：客显端口号选择（COM口配置）
- **波特率设置** (3685-3689行)：固定2400波特率
- **单品价格显示设置** (3691-3696行)：
  - 单价模式：显示商品单价
  - 小计模式：显示商品小计金额
- **客显检测功能** (3698-3700行)：端口连接状态检测

#### 1.2 副屏状态数据管理
**代码位置：** `src/store/show.js` (400-423行)
**数据结构：**
- **screen2ShowList** (400行)：副屏商品列表数据
- **screen2ReducePrice** (405行)：优惠金额显示
- **screen2TotalPrice** (407行)：总金额显示
- **screen2ReceiveMoney** (409行)：实收金额显示
- **screen2ReturnGoods** (411行)：退货状态标识
- **screen2ShowFinalPrice** (413行)：应收金额显示
- **screen2ShowMember** (415行)：会员信息显示控制
- **screen2MemberMoney** (416行)：会员余额
- **screen2MemberPayType** (418行)：会员价格类型（原价/会员价）
- **screen2ShowFollowErweima** (420行)：公众号二维码显示控制
- **screen2FollowErweima** (422行)：公众号二维码URL
- **screen2ErweimaMobile** (423行)：公众号手机号

#### 1.3 副屏HTML界面
**代码位置：** `static/screen2.html` (1-418行)
**界面设计：**
- **背景样式** (27-34行)：副屏背景图片和颜色配置
- **商品列表区域** (57-123行)：
  - 商品表格显示区域
  - 商品名称、数量、单价、小计展示
  - 滚动显示支持
- **广告显示区域** (36-77行)：副屏广告轮播显示区域
- **会员信息显示** (225-293行)：
  - 会员头像和姓名显示
  - 会员余额和积分展示
  - 会员卡号和电话显示
- **总价显示区域** (139-183行)：
  - 大字体总价显示
  - 优惠金额和实收金额展示

### 2. 客显硬件集成

#### 2.1 客显数据传输
**代码位置：**
- `src/page/pc/pay.vue` (2761-2775行) - 收银台客显控制
- `src/components/pc_final_pay.vue` (3015-3027行) - 支付页面客显控制

**功能特性：**
- **客显数据格式化** (2996-3014行)：
  - 应收金额显示
  - 找零金额计算和显示
  - 支付完成状态显示
- **实时价格传输** (2761-2775行)：
  - 商品扫描时实时显示价格
  - 支持单价和小计两种显示模式
  - 串口通信参数配置（波特率2400、数据位8、校验位0、停止位1）

#### 2.2 客显设置和检测
**代码位置：** `src/page/pc/setting.vue` (4929-4947行)
**功能特性：**
- **客显端口检测** (4929-4947行)：
  - 端口号验证
  - 客显设备连接测试
  - 显示数据格式验证（保留2位小数）
- **波特率和通信参数** (4938-4946行)：
  - 固定波特率2400
  - 数据位8位
  - 无校验位
  - 停止位1位

### 3. 副屏数据同步

#### 3.1 全局数据初始化
**代码位置：** `src/main.js` (223-249行)
**数据同步：**
- **Screen2data对象** (223-249行)：副屏数据全局管理
- **初始化函数** (224-249行)：副屏数据结构初始化
- **会员信息同步** (241-246行)：会员姓名、电话、积分、余额等信息同步
- **广告显示控制** (247行)：广告显示开关控制

#### 3.2 实时数据更新
**功能特性：**
- 收银过程中商品信息实时同步
- 会员信息动态更新
- 支付状态实时反馈
- 广告内容动态轮播

---

## 技术实现特点

### 1. 硬件集成
- 支持串口通信的客显设备
- 标准RS232/485通信协议
- 可配置端口号和通信参数

### 2. 数据同步机制
- Vuex状态管理实现数据共享
- 全局数据对象维护副屏状态
- 实时数据更新机制

### 3. 界面适配
- 响应式布局适配不同尺寸副屏
- 高对比度显示确保可读性
- 大字体设计提升客户体验

---

## 功能代码引用位置总结

| 功能模块 | 主要文件位置 | 关键代码行数 |
|---------|-------------|-------------|
| 副屏状态管理 | src/store/show.js | 400-423行 |
| 副屏设置配置 | src/page/pc/setting.vue | 3653-3703行, 4929-4947行 |
| 副屏HTML界面 | static/screen2.html | 1-418行（完整文件） |
| 客显硬件控制 | src/page/pc/pay.vue | 2761-2775行 |
| 支付客显控制 | src/components/pc_final_pay.vue | 2996-3027行 |
| 全局数据初始化 | src/main.js | 223-249行 |

---

## 备注
副屏模块为可选功能，需要配合硬件客显设备使用。该模块提升了收银透明度和客户体验，是现代POS系统的重要组成部分。
